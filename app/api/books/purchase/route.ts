import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  try {
    const { bookId, priceAmount } = await request.json()
    
    if (!bookId || !priceAmount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get book details
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        price_amount,
        user_id,
        users!inner(name, stripe_account_id)
      `)
      .eq('id', bookId)
      .eq('is_ebook', true)
      .single()

    if (bookError || !book) {
      return NextResponse.json(
        { error: 'Book not found' },
        { status: 404 }
      )
    }

    // Check if user already purchased this book
    const { data: existingPurchase } = await supabase
      .from('book_purchases')
      .select('id')
      .eq('user_id', user.id)
      .eq('project_id', bookId)
      .single()

    if (existingPurchase) {
      return NextResponse.json(
        { error: 'Book already purchased' },
        { status: 400 }
      )
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: book.title,
              description: `Digital book by ${book.users.name}`,
              images: [], // Add book cover if available
            },
            unit_amount: book.price_amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/books/${bookId}/read?purchase=success`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/books/${bookId}?purchase=cancelled`,
      metadata: {
        type: 'book_purchase',
        book_id: bookId,
        buyer_id: user.id,
        author_id: book.user_id,
      },
      // If author has Stripe Connect account, use it for direct payment
      ...(book.users.stripe_account_id && {
        payment_intent_data: {
          application_fee_amount: Math.round(book.price_amount * 0.05), // 5% platform fee
          transfer_data: {
            destination: book.users.stripe_account_id,
          },
        },
      }),
    })

    return NextResponse.json({ url: session.url })

  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
